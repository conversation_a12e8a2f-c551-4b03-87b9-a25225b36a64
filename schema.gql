# ------------------------------------------------------
# THIS FILE WAS AUTOMATICALLY GENERATED (DO NOT MODIFY)
# ------------------------------------------------------

type UserModel {
  id: ID!
  name: String!
  email: String!
  role: String!
}

type QuizQuestionModel {
  id: Int!
  questionText: String!
  options: [String!]!
  correctAnswerIndex: Int!
}

type QuizModel {
  id: Int!
  title: String!
  session: SessionModel!
  questions: [QuizQuestionModel!]!
  instructor: UserModel!
}

type QuestionModel {
  id: Int!
  content: String!
  answered: Boolean!
  options: [String!]!
  correctAnswerIndex: Int!
  userAnswers: [Int!]
  session: SessionModel!
  user: UserModel!
  quiz: QuizModel
}

type SessionModel {
  id: ID!
  title: String!
  startTime: DateTime!
  isLive: Boolean!
  instructor: UserModel!
  course: CourseModel!
  questions: [QuestionModel]
  quizzes: [QuizModel]
}

"""
A date-time string at UTC, such as 2019-12-03T09:54:33Z, compliant with the date-time format.
"""
scalar DateTime

type CourseModel {
  id: ID!
  title: String!
  description: String!
  instructor: UserModel!
  sessions: [SessionModel]
}

type Query {
  courses: [CourseModel!]!
  course(id: Int!): CourseModel!
  sessions: [SessionModel!]!
  session(id: Int!): SessionModel!
  quizzes: [QuizModel!]!
  quiz(id: Int!): QuizModel!
}

type Mutation {
  createCourse(createCourseInput: CreateCourseInput!): CourseModel!
  updateCourse(updateCourseInput: UpdateCourseInput!): CourseModel!
  removeCourse(id: Int!): Boolean!
  createSession(createSessionInput: CreateSessionInput!): SessionModel!
  updateSession(updateSessionInput: UpdateSessionInput!): SessionModel!
  removeSession(id: Int!): Boolean!
  startLiveSession(id: Int!): SessionModel!
  endLiveSession(id: Int!): SessionModel!
  createQuiz(createQuizInput: CreateQuizInput!): QuizModel!
  createQuizQuestion(quizId: Int!, createQuizQuestionInput: CreateQuizQuestionInput!): QuizQuestionModel!
  addQuestionsToQuiz(quizId: Int!, questionIds: [Int!]!): QuizModel!
}

input CreateCourseInput {
  title: String!
  description: String!
  instructorId: Int!
}

input UpdateCourseInput {
  title: String
  description: String
  instructorId: Int
  id: Int!
}

input CreateSessionInput {
  title: String!
  startTime: DateTime!
  isLive: Boolean!
  courseId: Int!
  instructorId: Int!
}

input UpdateSessionInput {
  title: String
  startTime: DateTime
  isLive: Boolean
  courseId: Int
  instructorId: Int
  id: Int!
}

input CreateQuizInput {
  title: String!
  sessionId: Int!
  questions: [CreateQuizQuestionInput!]
}

input CreateQuizQuestionInput {
  questionText: String!
  options: [String!]!
  correctAnswerIndex: Int!
}