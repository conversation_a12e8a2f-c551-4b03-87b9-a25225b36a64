import { Modu<PERSON> } from '@nestjs/common';
import { MessagesService } from './messages.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Message } from './entities/message.entity';
import { Session } from '../sessions/entities/session.entity';
import { UserEntity } from '../users/entities/user.entity';
import { MessagesController } from './messages.controller';
import { MessageResolver } from './graphql/resolvers/message.resolver';
import { SessionsModule } from '../sessions/sessions.module';

@Module({
  imports: [TypeOrmModule.forFeature([Message, Session, UserEntity]),
  SessionsModule,
],
  controllers: [MessagesController],
  providers: [MessagesService,
    MessageResolver,
  ],
  exports: [MessagesService],
})
export class MessagesModule {}
