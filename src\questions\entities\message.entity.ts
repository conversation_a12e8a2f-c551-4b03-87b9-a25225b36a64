import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, ManyToOne } from 'typeorm';
import { Session } from '../../sessions/entities/session.entity';
import { UserEntity } from '../../users/entities/user.entity';

@Entity()
export class Message {
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    content: string;

    @Column({ default: false })
    answered: boolean;

    @ManyToOne(() => Session, (session) => session.messages)
    session: Session;

    @ManyToOne(() => UserEntity, (user) => user.messages)
    user: UserEntity;
}