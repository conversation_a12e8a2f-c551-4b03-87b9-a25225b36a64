import { DataSource } from 'typeorm';
import { DatabaseSeeder } from './seed';
import { UserEntity } from '../../users/entities/user.entity';
import { Course } from '../../courses/entities/course.entity';
import { Session } from '../../sessions/entities/session.entity';
import { Message } from '../../messages/entities/message.entity';
import { Quiz } from '../../quizzes/entities/quiz.entity';
import { QuizQuestion } from '../../quizzes/entities/quiz-question.entity';
import { QuizAnswer } from '../../quizzes/entities/quiz-answer.entity';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function runSeed() {
    console.log('🚀 Initializing database connection...');

    const dataSource = new DataSource({
        type: 'mysql',
        host: process.env.DB_HOST || 'localhost',
        port: parseInt(process.env.DB_PORT, 10) || 3306,
        username: process.env.DB_USERNAME || 'root',
        password: process.env.DB_PASSWORD || '',
        database: process.env.DB_NAME || 'learning_platform',
        entities: [
            UserEntity,
            Course,
            Session,
            Message,
            Quiz,
            QuizQuestion,
            QuizAnswer,
        ],
        synchronize: true, // This will create tables if they don't exist
        logging: false,
    });

    try {
        await dataSource.initialize();
        console.log('✅ Database connection established');

        const seeder = new DatabaseSeeder(dataSource);
        await seeder.seed();

        console.log('\n🎉 Seeding completed successfully!');
        console.log('\n📋 Test Data Summary:');
        console.log('==========================================');
        console.log('👥 Users:');
        console.log('  Instructors:');
        console.log('    - Dr. John Smith (<EMAIL>)');
        console.log('    - Prof. Sarah Johnson (<EMAIL>)');
        console.log('  Students:');
        console.log('    - Alice Cooper (<EMAIL>) - ID: 3');
        console.log('    - Bob Wilson (<EMAIL>) - ID: 4');
        console.log('    - Charlie Brown (<EMAIL>) - ID: 5');
        console.log('    - Diana Prince (<EMAIL>) - ID: 6');
        console.log('    - Eva Martinez (<EMAIL>) - ID: 7');
        console.log('    - Frank Miller (<EMAIL>) - ID: 8');
        console.log('\n📚 Courses:');
        console.log('    - Introduction to Computer Science');
        console.log('    - Web Development Bootcamp');
        console.log('    - Data Structures and Algorithms');
        console.log('    - Database Design and Management');
        console.log('\n🎓 Sessions:');
        console.log('    - Session 1: Introduction to Programming (LIVE) - ID: 1');
        console.log('    - Session 2: HTML & CSS Fundamentals (LIVE) - ID: 2');
        console.log('    - Session 3: JavaScript Basics (Upcoming) - ID: 3');
        console.log('    - Session 4: Arrays and Linked Lists (LIVE) - ID: 4');
        console.log('    - Session 5: SQL Fundamentals (Upcoming) - ID: 5');
        console.log('\n💬 Messages:');
        console.log('    - Multiple chat messages in live sessions');
        console.log('    - Mix of instructor and student messages');
        console.log('    - Some read, some unread for testing');
        console.log('\n📝 Quizzes:');
        console.log('    - Programming Basics Quiz (Session 1)');
        console.log('    - HTML & CSS Basics Quiz (Session 2)');
        console.log('\n🔐 Login Credentials:');
        console.log('    All users have password: "password123"');
        console.log('\n🧪 Testing Recommendations:');
        console.log('==========================================');
        console.log('1. Use Session ID 1, 2, or 4 for chat testing (these are live)');
        console.log('2. Use User IDs 3-8 for student testing');
        console.log('3. Use User IDs 1-2 for instructor testing');
        console.log('4. Open multiple browser tabs with different User IDs');
        console.log('5. Test real-time messaging between different users');
        console.log('6. Check existing messages when joining sessions');
        console.log('\n🌐 Chat Test URLs:');
        console.log('   Open chat-test.html and use these settings:');
        console.log('   - Server URL: http://localhost:3000');
        console.log('   - Session ID: 1 (or 2, 4)');
        console.log('   - User ID: 3, 4, 5, 6, 7, or 8');
        console.log('   - User Name: Alice, Bob, Charlie, Diana, Eva, or Frank');

    } catch (error) {
        console.error('❌ Error during seeding:', error);
        process.exit(1);
    } finally {
        await dataSource.destroy();
        console.log('\n🔌 Database connection closed');
        process.exit(0);
    }
}

// Run the seeder
runSeed().catch((error) => {
    console.error('❌ Fatal error:', error);
    process.exit(1);
});
