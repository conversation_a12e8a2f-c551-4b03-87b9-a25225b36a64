# ------------------------------------------------------
# THIS FILE WAS AUTOMATICALLY GENERATED (DO NOT MODIFY)
# ------------------------------------------------------

type UserModel {
  id: ID!
  name: String!
  email: String!
  role: String!
}

type MessageModel {
  id: Int!
  content: String!
  timestamp: DateTime!
  isRead: Boolean!
  sender: UserModel!
  session: SessionModel!
}

"""
A date-time string at UTC, such as 2019-12-03T09:54:33Z, compliant with the date-time format.
"""
scalar DateTime

type QuizQuestionModel {
  id: Int!
  questionText: String!
  options: [String!]!
  correctAnswerIndex: Int!
}

type QuizModel {
  id: Int!
  title: String!
  session: SessionModel!
  questions: [QuizQuestionModel!]!
  instructor: UserModel!
}

type SessionModel {
  id: ID!
  title: String!
  startTime: DateTime!
  isLive: Boolean!
  instructor: UserModel!
  course: CourseModel!
  messages: [MessageModel]
  quizzes: [QuizModel]
}

type CourseModel {
  id: ID!
  title: String!
  description: String!
  instructor: UserModel!
  sessions: [SessionModel]
}

type Query {
  courses: [CourseModel!]!
  course(id: Int!): CourseModel!
  sessions: [SessionModel!]!
  session(id: Int!): SessionModel!
  messages: [MessageModel!]!
  message(id: Int!): MessageModel!
  messagesBySession(sessionId: Int!): [MessageModel!]!
  messagesByUser(userId: Int!): [MessageModel!]!
  recentMessages(sessionId: Int!, limit: Int! = 50): [MessageModel!]!
  unreadMessagesCount(sessionId: Int!, userId: Int!): Int!
  quizzes: [QuizModel!]!
  quiz(id: Int!): QuizModel!
}

type Mutation {
  createCourse(createCourseInput: CreateCourseInput!): CourseModel!
  updateCourse(updateCourseInput: UpdateCourseInput!): CourseModel!
  removeCourse(id: Int!): Boolean!
  createSession(createSessionInput: CreateSessionInput!): SessionModel!
  updateSession(updateSessionInput: UpdateSessionInput!): SessionModel!
  removeSession(id: Int!): Boolean!
  startLiveSession(id: Int!): SessionModel!
  endLiveSession(id: Int!): SessionModel!
  createMessage(createMessageInput: CreateMessageInput!): MessageModel!
  updateMessage(updateMessageInput: UpdateMessageInput!): MessageModel!
  removeMessage(id: Int!): Boolean!
  markMessageAsRead(id: Int!): MessageModel!
  markAllMessagesAsRead(sessionId: Int!, userId: Int!): Boolean!
  createQuiz(createQuizInput: CreateQuizInput!): QuizModel!
  createQuizQuestion(quizId: Int!, createQuizQuestionInput: CreateQuizQuestionInput!): QuizQuestionModel!
  addQuestionsToQuiz(quizId: Int!, questionIds: [Int!]!): QuizModel!
}

input CreateCourseInput {
  title: String!
  description: String!
  instructorId: Int!
}

input UpdateCourseInput {
  title: String
  description: String
  instructorId: Int
  id: Int!
}

input CreateSessionInput {
  title: String!
  startTime: DateTime!
  isLive: Boolean!
  courseId: Int!
  instructorId: Int!
}

input UpdateSessionInput {
  title: String
  startTime: DateTime
  isLive: Boolean
  courseId: Int
  instructorId: Int
  id: Int!
}

input CreateMessageInput {
  content: String!
  sessionId: Int!
  senderId: Int!
}

input UpdateMessageInput {
  id: Int!
  content: String
  isRead: Boolean
  sessionId: Int
  senderId: Int
}

input CreateQuizInput {
  title: String!
  sessionId: Int!
  questions: [CreateQuizQuestionInput!]
}

input CreateQuizQuestionInput {
  questionText: String!
  options: [String!]!
  correctAnswerIndex: Int!
}