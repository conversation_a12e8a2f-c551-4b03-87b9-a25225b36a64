import { ObjectType, Field, Int } from '@nestjs/graphql';
import { SessionModel } from '../../../sessions/graphql/models/session.model';
import { UserModel } from '../../../courses/graphql/models/user.model';
import { QuizModel } from '../../../quizzes/graphql/models/quiz.model';


@ObjectType('MessageModel')
export class MessageModel {
    @Field(() => Int)
    id: number;

    @Field()
    content: string;

    @Field(() => Boolean)
    answered: boolean;

    @Field(() => [String])
    options: string[];

    @Field(() => Int)
    correctAnswerIndex: number;

    @Field(() => [Int], { nullable: true })
    userAnswers?: number[];

    @Field(() => SessionModel)
    session: SessionModel;

    @Field(() => UserModel)
    user: UserModel;

    @Field(() => QuizModel, { nullable: true })
    quiz?: QuizModel;
}
