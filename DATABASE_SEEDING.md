# 🌱 Database Seeding Guide

This guide will help you populate your database with test data for the learning platform chat system.

## 🚀 Quick Start

1. **Make sure your database is running** and your `.env` file is configured correctly.

2. **Run the seeding command:**
   ```bash
   npm run seed
   ```

3. **Start your application:**
   ```bash
   npm run start:dev
   ```

4. **Open the chat test interface** (`chat-test.html`) and start testing!

## 📊 What Gets Seeded

### 👥 Users (8 total)
- **2 Instructors:**
  - Dr. <PERSON> (ID: 1) - `<EMAIL>`
  - <PERSON><PERSON> <PERSON> (ID: 2) - `<EMAIL>`

- **6 Students:**
  - <PERSON> (ID: 3) - `<EMAIL>`
  - <PERSON> (ID: 4) - `<EMAIL>`
  - <PERSON> (ID: 5) - `<EMAIL>`
  - <PERSON> (ID: 6) - `<EMAIL>`
  - <PERSON> (ID: 7) - `<EMAIL>`
  - <PERSON> (ID: 8) - `<EMAIL>`

**All users have password:** `password123`

### 📚 Courses (4 total)
- Introduction to Computer Science
- Web Development Bootcamp
- Data Structures and Algorithms
- Database Design and Management

### 🎓 Sessions (5 total)
- **Session 1:** Introduction to Programming (LIVE) ✅
- **Session 2:** HTML & CSS Fundamentals (LIVE) ✅
- **Session 3:** JavaScript Basics (Upcoming)
- **Session 4:** Arrays and Linked Lists (LIVE) ✅
- **Session 5:** SQL Fundamentals (Upcoming)

### 💬 Messages
- Multiple chat messages in live sessions
- Mix of instructor and student messages
- Some messages marked as read, others unread
- Realistic conversation flow with timestamps

### 📝 Quizzes
- Programming Basics Quiz (Session 1)
- HTML & CSS Basics Quiz (Session 2)

## 🧪 Testing Scenarios

### **Multi-User Chat Testing:**
1. Open multiple browser tabs
2. Use different User IDs (3, 4, 5, 6, 7, 8)
3. Connect all to the same Session ID (1, 2, or 4)
4. Test real-time messaging

### **Recommended Test Settings:**

**Tab 1 (Alice):**
- Server URL: `http://localhost:3000`
- Session ID: `1`
- User ID: `3`
- User Name: `Alice Cooper`

**Tab 2 (Bob):**
- Server URL: `http://localhost:3000`
- Session ID: `1`
- User ID: `4`
- User Name: `Bob Wilson`

**Tab 3 (Charlie):**
- Server URL: `http://localhost:3000`
- Session ID: `1`
- User ID: `5`
- User Name: `Charlie Brown`

### **Features to Test:**
- ✅ Real-time messaging
- ✅ Typing indicators
- ✅ Read receipts
- ✅ Message history loading
- ✅ Join/Leave session
- ✅ Multiple users in same session
- ✅ Connection/Disconnection handling

## 🔧 Troubleshooting

### **Database Connection Issues:**
- Ensure your MySQL server is running
- Check your `.env` file configuration:
  ```env
  DB_HOST=localhost
  DB_PORT=3306
  DB_USERNAME=your_username
  DB_PASSWORD=your_password
  DB_NAME=learning_platform
  ```

### **Seeding Errors:**
- Make sure the database exists
- Check if all dependencies are installed: `npm install`
- Verify TypeORM configuration in your app

### **Chat Not Working:**
- Ensure the server is running on the correct port
- Check browser console for WebSocket connection errors
- Verify CORS settings in your NestJS app

## 🔄 Re-seeding

To clear and re-seed the database:
```bash
npm run seed
```

The seeder automatically clears existing data before inserting new data.

## 📱 API Testing

You can also test the REST API endpoints:

```bash
# Get messages for session 1
curl http://localhost:3000/messages/session/1

# Get recent messages
curl http://localhost:3000/messages/session/1/recent?limit=10

# Send a message (POST)
curl -X POST http://localhost:3000/messages \
  -H "Content-Type: application/json" \
  -d '{"content":"Test message","sessionId":1,"senderId":3}'
```

## 🎯 Next Steps

1. Run the seeder
2. Start your NestJS application
3. Open `chat-test.html` in multiple browser tabs
4. Test the real-time chat functionality
5. Explore the GraphQL playground at `http://localhost:3000/graphql`

Happy testing! 🚀
