import { DataSource } from 'typeorm';
import { UserEntity } from '../../users/entities/user.entity';
import { Course } from '../../courses/entities/course.entity';
import { Session } from '../../sessions/entities/session.entity';
import { Message } from '../../messages/entities/message.entity';
import { Quiz } from '../../quizzes/entities/quiz.entity';
import { QuizQuestion } from '../../quizzes/entities/quiz-question.entity';
import { UserRoleEnum } from '../../enums/user-role.enum';
import * as bcrypt from 'bcrypt';

export class DatabaseSeeder {
    constructor(private dataSource: DataSource) {}

    async seed() {
        console.log('🌱 Starting database seeding...');

        // Clear existing data
        await this.clearDatabase();

        // Seed data
        const users = await this.seedUsers();
        const courses = await this.seedCourses(users);
        const sessions = await this.seedSessions(courses, users);
        const messages = await this.seedMessages(sessions, users);
        const quizzes = await this.seedQuizzes(sessions, users);

        console.log('✅ Database seeding completed successfully!');
        console.log(`Created: ${users.length} users, ${courses.length} courses, ${sessions.length} sessions, ${messages.length} messages, ${quizzes.length} quizzes`);
    }

    private async clearDatabase() {
        console.log('🧹 Clearing existing data...');
        
        const entities = [Message, QuizQuestion, Quiz, Session, Course, UserEntity];
        
        for (const entity of entities) {
            const repository = this.dataSource.getRepository(entity);
            await repository.delete({});
        }
    }

    private async seedUsers(): Promise<UserEntity[]> {
        console.log('👥 Seeding users...');
        
        const userRepository = this.dataSource.getRepository(UserEntity);
        const salt = await bcrypt.genSalt();

        const usersData = [
            {
                name: 'Dr. John Smith',
                email: '<EMAIL>',
                password: await bcrypt.hash('password123', salt),
                salt,
                role: UserRoleEnum.INSTRUCTOR,
            },
            {
                name: 'Prof. Sarah Johnson',
                email: '<EMAIL>',
                password: await bcrypt.hash('password123', salt),
                salt,
                role: UserRoleEnum.INSTRUCTOR,
            },
            {
                name: 'Alice Cooper',
                email: '<EMAIL>',
                password: await bcrypt.hash('password123', salt),
                salt,
                role: UserRoleEnum.STUDENT,
            },
            {
                name: 'Bob Wilson',
                email: '<EMAIL>',
                password: await bcrypt.hash('password123', salt),
                salt,
                role: UserRoleEnum.STUDENT,
            },
            {
                name: 'Charlie Brown',
                email: '<EMAIL>',
                password: await bcrypt.hash('password123', salt),
                salt,
                role: UserRoleEnum.STUDENT,
            },
            {
                name: 'Diana Prince',
                email: '<EMAIL>',
                password: await bcrypt.hash('password123', salt),
                salt,
                role: UserRoleEnum.STUDENT,
            },
            {
                name: 'Eva Martinez',
                email: '<EMAIL>',
                password: await bcrypt.hash('password123', salt),
                salt,
                role: UserRoleEnum.STUDENT,
            },
            {
                name: 'Frank Miller',
                email: '<EMAIL>',
                password: await bcrypt.hash('password123', salt),
                salt,
                role: UserRoleEnum.STUDENT,
            }
        ];

        const users = userRepository.create(usersData);
        return await userRepository.save(users);
    }

    private async seedCourses(users: UserEntity[]): Promise<Course[]> {
        console.log('📚 Seeding courses...');
        
        const courseRepository = this.dataSource.getRepository(Course);
        const instructors = users.filter(user => user.role === UserRoleEnum.INSTRUCTOR);

        const coursesData = [
            {
                title: 'Introduction to Computer Science',
                description: 'Learn the fundamentals of programming and computer science concepts.',
                instructor: instructors[0],
            },
            {
                title: 'Web Development Bootcamp',
                description: 'Full-stack web development with modern technologies.',
                instructor: instructors[0],
            },
            {
                title: 'Data Structures and Algorithms',
                description: 'Master essential data structures and algorithmic thinking.',
                instructor: instructors[1],
            },
            {
                title: 'Database Design and Management',
                description: 'Learn to design and manage relational databases.',
                instructor: instructors[1],
            }
        ];

        const courses = courseRepository.create(coursesData);
        return await courseRepository.save(courses);
    }

    private async seedSessions(courses: Course[], users: UserEntity[]): Promise<Session[]> {
        console.log('🎓 Seeding sessions...');
        
        const sessionRepository = this.dataSource.getRepository(Session);
        const now = new Date();

        const sessionsData = [
            {
                title: 'Introduction to Programming - Week 1',
                startTime: new Date(now.getTime() - 2 * 60 * 60 * 1000), // 2 hours ago
                isLive: true,
                course: courses[0],
                instructor: courses[0].instructor,
            },
            {
                title: 'HTML & CSS Fundamentals',
                startTime: new Date(now.getTime() - 1 * 60 * 60 * 1000), // 1 hour ago
                isLive: true,
                course: courses[1],
                instructor: courses[1].instructor,
            },
            {
                title: 'JavaScript Basics',
                startTime: new Date(now.getTime() + 1 * 60 * 60 * 1000), // 1 hour from now
                isLive: false,
                course: courses[1],
                instructor: courses[1].instructor,
            },
            {
                title: 'Arrays and Linked Lists',
                startTime: new Date(now.getTime() - 30 * 60 * 1000), // 30 minutes ago
                isLive: true,
                course: courses[2],
                instructor: courses[2].instructor,
            },
            {
                title: 'SQL Fundamentals',
                startTime: new Date(now.getTime() + 2 * 60 * 60 * 1000), // 2 hours from now
                isLive: false,
                course: courses[3],
                instructor: courses[3].instructor,
            }
        ];

        const sessions = sessionRepository.create(sessionsData);
        return await sessionRepository.save(sessions);
    }

    private async seedMessages(sessions: Session[], users: UserEntity[]): Promise<Message[]> {
        console.log('💬 Seeding messages...');
        
        const messageRepository = this.dataSource.getRepository(Message);
        const students = users.filter(user => user.role === UserRoleEnum.STUDENT);
        const instructors = users.filter(user => user.role === UserRoleEnum.INSTRUCTOR);
        
        const messagesData = [];
        const now = new Date();

        // Messages for Session 1 (Introduction to Programming)
        const session1Messages = [
            {
                content: 'Welcome everyone to our first programming session! 👋',
                sender: sessions[0].instructor,
                session: sessions[0],
                timestamp: new Date(now.getTime() - 90 * 60 * 1000), // 90 minutes ago
                isRead: true,
            },
            {
                content: 'Hello Professor! Excited to learn programming!',
                sender: students[0], // Alice
                session: sessions[0],
                timestamp: new Date(now.getTime() - 89 * 60 * 1000),
                isRead: true,
            },
            {
                content: 'Hi everyone! Looking forward to this course 🚀',
                sender: students[1], // Bob
                session: sessions[0],
                timestamp: new Date(now.getTime() - 88 * 60 * 1000),
                isRead: true,
            },
            {
                content: 'Today we\'ll cover variables, data types, and basic operations. Any questions before we start?',
                sender: sessions[0].instructor,
                session: sessions[0],
                timestamp: new Date(now.getTime() - 85 * 60 * 1000),
                isRead: false,
            },
            {
                content: 'What programming language will we be using?',
                sender: students[2], // Charlie
                session: sessions[0],
                timestamp: new Date(now.getTime() - 84 * 60 * 1000),
                isRead: false,
            },
            {
                content: 'We\'ll start with Python as it\'s beginner-friendly and widely used in the industry.',
                sender: sessions[0].instructor,
                session: sessions[0],
                timestamp: new Date(now.getTime() - 83 * 60 * 1000),
                isRead: false,
            },
            {
                content: 'Perfect! I\'ve heard Python is great for beginners.',
                sender: students[3], // Diana
                session: sessions[0],
                timestamp: new Date(now.getTime() - 82 * 60 * 1000),
                isRead: false,
            }
        ];

        // Messages for Session 2 (HTML & CSS)
        const session2Messages = [
            {
                content: 'Good morning class! Ready to dive into web development? 🌐',
                sender: sessions[1].instructor,
                session: sessions[1],
                timestamp: new Date(now.getTime() - 50 * 60 * 1000), // 50 minutes ago
                isRead: true,
            },
            {
                content: 'Yes! Can\'t wait to build my first website!',
                sender: students[4], // Eva
                session: sessions[1],
                timestamp: new Date(now.getTime() - 49 * 60 * 1000),
                isRead: true,
            },
            {
                content: 'Will we be using any frameworks or just vanilla HTML/CSS?',
                sender: students[5], // Frank
                session: sessions[1],
                timestamp: new Date(now.getTime() - 48 * 60 * 1000),
                isRead: false,
            },
            {
                content: 'We\'ll start with vanilla HTML and CSS to understand the fundamentals first.',
                sender: sessions[1].instructor,
                session: sessions[1],
                timestamp: new Date(now.getTime() - 47 * 60 * 1000),
                isRead: false,
            },
            {
                content: 'That makes sense. Building a strong foundation is important.',
                sender: students[0], // Alice
                session: sessions[1],
                timestamp: new Date(now.getTime() - 46 * 60 * 1000),
                isRead: false,
            }
        ];

        // Messages for Session 4 (Arrays and Linked Lists)
        const session4Messages = [
            {
                content: 'Let\'s explore data structures today! We\'ll start with arrays. 📊',
                sender: sessions[3].instructor,
                session: sessions[3],
                timestamp: new Date(now.getTime() - 25 * 60 * 1000), // 25 minutes ago
                isRead: true,
            },
            {
                content: 'I\'ve been struggling with linked lists. Hope this helps!',
                sender: students[1], // Bob
                session: sessions[3],
                timestamp: new Date(now.getTime() - 24 * 60 * 1000),
                isRead: false,
            },
            {
                content: 'Don\'t worry Bob, we\'ll go through it step by step with examples.',
                sender: sessions[3].instructor,
                session: sessions[3],
                timestamp: new Date(now.getTime() - 23 * 60 * 1000),
                isRead: false,
            },
            {
                content: 'What\'s the main difference between arrays and linked lists?',
                sender: students[2], // Charlie
                session: sessions[3],
                timestamp: new Date(now.getTime() - 22 * 60 * 1000),
                isRead: false,
            },
            {
                content: 'Great question! Arrays have fixed size and contiguous memory, while linked lists are dynamic.',
                sender: sessions[3].instructor,
                session: sessions[3],
                timestamp: new Date(now.getTime() - 21 * 60 * 1000),
                isRead: false,
            }
        ];

        messagesData.push(...session1Messages, ...session2Messages, ...session4Messages);

        const messages = messageRepository.create(messagesData);
        return await messageRepository.save(messages);
    }

    private async seedQuizzes(sessions: Session[], users: UserEntity[]): Promise<Quiz[]> {
        console.log('📝 Seeding quizzes...');
        
        const quizRepository = this.dataSource.getRepository(Quiz);
        const quizQuestionRepository = this.dataSource.getRepository(QuizQuestion);

        // Create quiz for Session 1
        const quiz1 = quizRepository.create({
            title: 'Programming Basics Quiz',
            session: sessions[0],
        });
        const savedQuiz1 = await quizRepository.save(quiz1);

        const quiz1Questions = [
            {
                questionText: 'What is a variable in programming?',
                options: ['A fixed value', 'A container for storing data', 'A type of loop', 'A function'],
                correctAnswerIndex: 1,
                quiz: savedQuiz1,
            },
            {
                questionText: 'Which of these is a Python data type?',
                options: ['int', 'string', 'boolean', 'All of the above'],
                correctAnswerIndex: 3,
                quiz: savedQuiz1,
            }
        ];

        await quizQuestionRepository.save(quiz1Questions);

        // Create quiz for Session 2
        const quiz2 = quizRepository.create({
            title: 'HTML & CSS Basics',
            session: sessions[1],
        });
        const savedQuiz2 = await quizRepository.save(quiz2);

        const quiz2Questions = [
            {
                questionText: 'What does HTML stand for?',
                options: ['Hyper Text Markup Language', 'High Tech Modern Language', 'Home Tool Markup Language', 'Hyperlink and Text Markup Language'],
                correctAnswerIndex: 0,
                quiz: savedQuiz2,
            },
            {
                questionText: 'Which CSS property is used to change text color?',
                options: ['font-color', 'text-color', 'color', 'foreground-color'],
                correctAnswerIndex: 2,
                quiz: savedQuiz2,
            }
        ];

        await quizQuestionRepository.save(quiz2Questions);

        return [savedQuiz1, savedQuiz2];
    }
}
