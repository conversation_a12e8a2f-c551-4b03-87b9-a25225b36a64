# ------------------------------------------------------
# THIS FILE WAS AUTOMATICALLY GENERATED (DO NOT MODIFY)
# ------------------------------------------------------

type CourseModel {
  description: String!
  id: ID!
  instructor: UserModel!
  sessions: [SessionModel]
  title: String!
}

input CreateCourseInput {
  description: String!
  instructorId: Int!
  title: String!
}

"""
A date-time string at UTC, such as 2019-12-03T09:54:33Z, compliant with the date-time format.
"""
scalar DateTime

type Mutation {
  createCourse(createCourseInput: CreateCourseInput!): CourseModel!
  removeCourse(id: Int!): Boolean!
  updateCourse(updateCourseInput: UpdateCourseInput!): CourseModel!
}

type Query {
  course(id: Int!): CourseModel!
  courses: [CourseModel!]!
}

type SessionModel {
  id: ID!
  instructor: UserModel!
  isLive: Boolean!
  startTime: DateTime!
  title: String!
}

input UpdateCourseInput {
  description: String
  id: Int!
  instructorId: Int
  title: String
}

type UserModel {
  email: String!
  id: ID!
  name: String!
  role: String!
}