import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Message } from './entities/message.entity';
import { CreateMessageDto } from './dto/create-message.dto';
import { UpdateMessageDto } from './dto/update-message.dto';
import { Session } from '../sessions/entities/session.entity';
import { UserEntity } from '../users/entities/user.entity';

@Injectable()
export class MessagesService {
    constructor(
        @InjectRepository(Message)
        private readonly messagesRepo: Repository<Message>,
        @InjectRepository(Session)
        private readonly sessionsRepo: Repository<Session>,
        @InjectRepository(UserEntity)
        private readonly usersRepo: Repository<UserEntity>,
    ) {}

    async create(createDto: CreateMessageDto): Promise<Message> {
        const session = await this.sessionsRepo.findOneBy({ id: createDto.sessionId });
        if (!session) throw new NotFoundException('Session not found');

        const user = await this.usersRepo.findOneBy({ id: createDto.userId });
        if (!user) throw new NotFoundException('User not found');

        const message = this.messagesRepo.create({
            content: createDto.content,
            answered: false,
            session,
            user,
        });
        return this.messagesRepo.save(message);
    }

    findAll(): Promise<Message[]> {
        return this.messagesRepo.find();
    }

    async findOne(id: number): Promise<Message> {
        const message = await this.messagesRepo.findOneBy({ id });
        if (!message) throw new NotFoundException('Message not found');
        return message;
    }

    async update(id: number, updateDto: UpdateMessageDto): Promise<Message> {
        const message = await this.findOne(id);

        if (updateDto.sessionId !== undefined) {
            const session = await this.sessionsRepo.findOneBy({ id: updateDto.sessionId });
            if (!session) throw new NotFoundException('Session not found');
            message.session = session;
        }

        if (updateDto.userId !== undefined) {
            const user = await this.usersRepo.findOneBy({ id: updateDto.userId });
            if (!user) throw new NotFoundException('User not found');
            message.user = user;
        }

        // Filter out sessionId and userId since they are not columns
        const { sessionId, userId, ...restDto } = updateDto;
        Object.assign(message, restDto);

        return this.messagesRepo.save(message);
    }

    async remove(id: number): Promise<void> {
        const message = await this.findOne(id);
        await this.messagesRepo.remove(message);
    }

    async markAsAnswered(id: number): Promise<Message> {
        const message = await this.findOne(id);
        message.answered = true;
        return this.messagesRepo.save(message);
    }

    async getMessagesBySession(sessionId: number): Promise<Message[]> {
        return this.messagesRepo.find({
            where: {
                session: { id: sessionId },
            },
            relations: ['user'],
        });
    }

    async getMessagesByUser(userId: number): Promise<Message[]> {
        return this.messagesRepo.find({
            where: {
                user: { id: userId },
            },
            relations: ['session'],
        });
    }

    async getUser(messageId: number): Promise<UserEntity> {
        const message = await this.messagesRepo.findOne({
            where: { id: messageId },
            relations: ['user'],
        });
        if (!message) throw new NotFoundException('Message not found');
        return message.user;
    }

    async getSession(messageId: number): Promise<Session> {
        const message = await this.messagesRepo.findOne({
            where: { id: messageId },
            relations: ['session'],
        });
        if (!message) throw new NotFoundException('Message not found');
        return message.session;
    }
}
