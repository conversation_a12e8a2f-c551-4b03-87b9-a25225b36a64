import {
  WebSocketGateway,
  SubscribeMessage,
  MessageBody,
  WebSocketServer,
  ConnectedSocket,
  OnGatewayConnection,
  OnGatewayDisconnect,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { MessagesService } from './messages.service';
import { CreateMessageDto } from './dto/create-message.dto';
import { Logger } from '@nestjs/common';

@WebSocketGateway({
  cors: {
    origin: '*',
  },
  namespace: '/chat',
})
export class MessagesGateway implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  private logger = new Logger('MessagesGateway');

  constructor(private readonly messagesService: MessagesService) {}

  handleConnection(client: Socket) {
    this.logger.log(`Client connected: ${client.id}`);
  }

  handleDisconnect(client: Socket) {
    this.logger.log(`Client disconnected: ${client.id}`);
  }

  @SubscribeMessage('joinSession')
  async handleJoinSession(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { sessionId: number; userId: number }
  ) {
    const room = `session_${data.sessionId}`;
    await client.join(room);
    this.logger.log(`User ${data.userId} joined session ${data.sessionId}`);
    
    // Send recent messages to the newly joined user
    const recentMessages = await this.messagesService.getRecentMessages(data.sessionId, 50);
    client.emit('recentMessages', recentMessages);
  }

  @SubscribeMessage('leaveSession')
  async handleLeaveSession(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { sessionId: number; userId: number }
  ) {
    const room = `session_${data.sessionId}`;
    await client.leave(room);
    this.logger.log(`User ${data.userId} left session ${data.sessionId}`);
  }

  @SubscribeMessage('sendMessage')
  async handleSendMessage(
    @ConnectedSocket() client: Socket,
    @MessageBody() createMessageDto: CreateMessageDto
  ) {
    try {
      // Create the message
      const message = await this.messagesService.create(createMessageDto);
      
      // Get the full message with relations
      const fullMessage = await this.messagesService.findOne(message.id);
      
      // Emit to all users in the session room
      const room = `session_${createMessageDto.sessionId}`;
      this.server.to(room).emit('newMessage', fullMessage);
      
      this.logger.log(`Message sent to session ${createMessageDto.sessionId}`);
    } catch (error) {
      client.emit('error', { message: 'Failed to send message', error: error.message });
    }
  }

  @SubscribeMessage('markAsRead')
  async handleMarkAsRead(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { messageId: number; userId: number }
  ) {
    try {
      await this.messagesService.markAsRead(data.messageId);
      
      // Optionally emit read status to other users
      const message = await this.messagesService.findOne(data.messageId);
      const room = `session_${message.session.id}`;
      this.server.to(room).emit('messageRead', { messageId: data.messageId, userId: data.userId });
    } catch (error) {
      client.emit('error', { message: 'Failed to mark message as read', error: error.message });
    }
  }

  @SubscribeMessage('typing')
  async handleTyping(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { sessionId: number; userId: number; isTyping: boolean }
  ) {
    const room = `session_${data.sessionId}`;
    // Broadcast typing status to other users in the room (excluding sender)
    client.to(room).emit('userTyping', {
      userId: data.userId,
      isTyping: data.isTyping,
    });
  }
}
